# Развертывание на Vercel

Это руководство поможет вам развернуть приложение Ayah Verse Editor на платформе Vercel.

## Предварительные требования

1. **Аккаунт Vercel**: Зарегистрируйтесь на [vercel.com](https://vercel.com)
2. **Gemini API Key**: Получите API ключ от Google AI Studio
3. **Git репозиторий**: Ваш код должен быть в Git репозитории (GitHub, GitLab, Bitbucket)

## Способ 1: Развертывание через Vercel CLI

### Шаг 1: Установка Vercel CLI
```bash
npm install -g vercel
```

### Шаг 2: Вход в аккаунт
```bash
vercel login
```

### Шаг 3: Развертывание
```bash
vercel
```

Следуйте инструкциям CLI:
- Выберите область (scope)
- Подтвердите настройки проекта
- Дождитесь завершения развертывания

### Шаг 4: Настройка переменных окружения
```bash
vercel env add GEMINI_API_KEY
```
Введите ваш Gemini API ключ когда будет запрошено.

### Шаг 5: Повторное развертывание
```bash
vercel --prod
```

## Способ 2: Развертывание через веб-интерфейс Vercel

### Шаг 1: Подключение репозитория
1. Войдите в [vercel.com](https://vercel.com)
2. Нажмите "New Project"
3. Импортируйте ваш Git репозиторий

### Шаг 2: Настройка проекта
Vercel автоматически определит настройки:
- **Framework Preset**: Vite
- **Build Command**: `npm run build`
- **Output Directory**: `dist`
- **Install Command**: `npm install`

### Шаг 3: Настройка переменных окружения
1. В настройках проекта перейдите в "Environment Variables"
2. Добавьте переменную:
   - **Name**: `GEMINI_API_KEY`
   - **Value**: ваш Gemini API ключ
   - **Environments**: Production, Preview, Development

### Шаг 4: Развертывание
Нажмите "Deploy" и дождитесь завершения.

## Получение Gemini API Key

1. Перейдите на [ai.google.dev](https://ai.google.dev)
2. Нажмите "Get API key"
3. Создайте новый проект или выберите существующий
4. Создайте API ключ
5. Скопируйте ключ для использования в Vercel

## Проверка развертывания

После успешного развертывания:
1. Откройте предоставленный URL
2. Проверьте, что приложение загружается
3. Протестируйте функции AI (выделение слов, применение кашиды)

## Устранение неполадок

### Ошибка сборки
- Убедитесь, что все зависимости установлены
- Проверьте, что `npm run build` работает локально

### Ошибки API
- Проверьте правильность GEMINI_API_KEY
- Убедитесь, что API ключ активен и имеет необходимые разрешения

### Проблемы с маршрутизацией
- Файл `vercel.json` настроен для SPA приложений
- Все маршруты перенаправляются на `index.html`

## Автоматические развертывания

После первоначальной настройки Vercel будет автоматически развертывать изменения при каждом push в основную ветку репозитория.

## Дополнительные настройки

### Пользовательский домен
1. В настройках проекта перейдите в "Domains"
2. Добавьте ваш домен
3. Настройте DNS записи согласно инструкциям

### Мониторинг
Vercel предоставляет встроенную аналитику и мониторинг производительности в разделе "Analytics".
